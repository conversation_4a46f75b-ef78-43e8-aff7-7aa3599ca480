# Using template from https://github.com/invenia/PkgTemplates.jl/blob/master/test/fixtures/DocumenterGitHubActions/.github/workflows/ci.yml
name: CI
on:
  - push
  - pull_request
jobs:
  test:
    name: <PERSON> ${{ matrix.version }} - ${{ matrix.os }} - ${{ matrix.arch }} - ${{ github.event_name }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        version:
          - '1'       # latest release
          - 'nightly' # latest master
        os:
          - ubuntu-latest
          - windows-latest
          - macos-latest
        arch:
          - x64
    steps:
      - uses: actions/checkout@v4
      - uses: julia-actions/setup-julia@v1
        with:
          version: ${{ matrix.version }}
          arch: ${{ matrix.arch }}
      - uses: julia-actions/cache@v1
      - run: | # force use of the local version of Bravais (instead of just the latest registered version)
          julia --color=yes --project -e '
            using Pkg
            Pkg.develop(PackageSpec(path=joinpath(pwd(), "Bravais")))'
        shell: bash # avoid escaping issues w/ pwsh on Windows (cf. https://github.com/julia-actions/setup-julia#matrix-testing)
      - uses: julia-actions/julia-buildpkg@v1
      - uses: julia-actions/julia-runtest@v1
        continue-on-error: ${{ matrix.version == 'nightly' }}
      - uses: julia-actions/julia-processcoverage@v1
      - uses: codecov/codecov-action@v5
        with:
          files: lcov.info
