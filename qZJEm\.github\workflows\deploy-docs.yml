# Using template from https://juliadocs.github.io/Documenter.jl/stable/man/hosting/#GitHub-Actions
name: Documentation

on:
  push:
    branches:
      - master
    tags: '*'
  pull_request:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: julia-actions/setup-julia@latest
        with:
          version: '1' # latest release
      - name: Install dependencies
        run: |
          julia --project=docs/ -e '
            using Pkg
            Pkg.develop(PackageSpec(path=joinpath(pwd(), "Bravais")))
            Pkg.develop(PackageSpec(path=pwd()))
            Pkg.instantiate()'
      - name: Build and deploy
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} # For authentication with GitHub Actions token
          DOCUMENTER_KEY: ${{ secrets.DOCUMENTER_KEY }} # For authentication with SSH deploy key
        run: julia --project=docs/ docs/make.jl